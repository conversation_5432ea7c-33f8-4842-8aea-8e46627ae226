from flask import Blueprint, request, jsonify
from ...config import DatabaseConfig
from ...app import app
from ...utils import handle_api_exceptions, handle_database_exceptions, ApiValidator  # type: ignore
from ...validator import (
    validate_request,
    with_database_doc,
    validate,
    db_helper,
    RequestValidator,
    ValidationError,
)
import sys
from typing import Any, Dict, Optional
from pymongo.collection import Collection
from flask_jwt_extended import jwt_required  # type: ignore
from ...type import ApiResponse, DataType, SceneInfo
from datetime import datetime

data_analysis = Blueprint("data_analysis", __name__)

# 使用配置类
db_config = DatabaseConfig()
mongo_db = db_config.init_app(app)

# 检查数据库连接是否成功
if mongo_db is None:
    print("错误：数据库连接失败，程序退出")
    sys.exit(1)

db_base_data_radar: Collection[Dict[str, Any]] = mongo_db["radar"]
db_base_data_scene: Collection[Dict[str, Any]] = mongo_db["scene"]


# 获取雷达场景坐标 - 原版本（保留作为对比）
@data_analysis.route("/get_scene_coordinates_old", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取场景坐标")
@handle_database_exceptions
def get_scene_coordinates_old() -> ApiResponse:
    """获取场景坐标 - 原版本"""
    data: Optional[DataType] = request.get_json()
    if error := ApiValidator.validate_request_data(data):
        return error
    assert data is not None

    scene_id: Optional[str] = data.get("scene_ID")
    if error := ApiValidator.validate_id(scene_id, "scene_ID"):
        return error
    assert scene_id is not None

    db_scene_doc: Optional[SceneInfo] = db_base_data_scene.find_one({"_id": scene_id})
    if db_scene_doc is None:
        return jsonify({"error": "场景不存在", "code": "SCENE_NOT_FOUND"}), 404

    send_data = db_scene_doc.get("coordinates")
    if send_data is None:
        return (
            jsonify({"status": "error", "message": "场景坐标不存在", "data": []}),
            404,
        )

    if len(send_data) == 0:
        return (
            jsonify({"status": "error", "message": "雷达未设置场景坐标", "data": []}),
            404,
        )

    return (
        jsonify(
            {"status": "success", "message": "雷达场景坐标加载成功", "data": send_data}
        ),
        200,
    )


# 获取雷达场景坐标 - 新版本（使用验证框架）
@data_analysis.route("/get_scene_coordinates", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取场景坐标")
@handle_database_exceptions
def get_scene_coordinates() -> ApiResponse:
    """获取场景坐标 - 使用新验证框架的简化版本"""
    try:
        # 验证请求数据和必需字段
        data = RequestValidator.get_json_or_fail()
        scene_id = (
            RequestValidator.validate_field(data, "scene_ID")
            .required()
            .not_empty()
            .unwrap()
        )

        # 使用数据库助手查询场景
        helper = db_helper(db_base_data_scene)
        scene = helper.find_one_or_fail(
            {"_id": scene_id}, "场景不存在", "SCENE_NOT_FOUND"
        )

        # 验证坐标字段
        coordinates = (
            validate(scene.get("coordinates"), "场景坐标")
            .required("场景坐标不存在")
            .not_empty("雷达未设置场景坐标")
            .unwrap()
        )

        return (
            jsonify(
                {
                    "status": "success",
                    "message": "雷达场景坐标加载成功",
                    "data": coordinates,
                }
            ),
            200,
        )

    except ValidationError as e:
        return e.to_response()


# 获取雷达主机 - 原版本（保留作为对比）
@data_analysis.route("/get_radar_host_old", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取雷达主机")
@handle_database_exceptions
def web_get_radar_host_old() -> ApiResponse:
    data = request.get_json()
    if (error := ApiValidator.validate_request_data(data)) is not None:
        return error
    assert data is not None

    radar_id = data.get("radar_ID", "")
    if (error := ApiValidator.validate_id(radar_id, "radar_ID")) is not None:
        return error
    assert radar_id is not None

    db_radar_doc = db_base_data_radar.find_one({"ID": radar_id})
    if db_radar_doc is None:
        return jsonify({"error": "雷达不存在", "code": "RADAR_NOT_FOUND"}), 404

    send_data = db_radar_doc.get("coordinates", {})
    if len(send_data) == 0:
        return (
            jsonify({"status": "warning", "message": "雷达未设置坐标", "data": []}),
            500,
        )

    return (
        jsonify(
            {"status": "success", "message": "雷达坐标加载成功", "data": send_data}
        ),
        200,
    )


# 获取雷达主机 - 新版本（使用验证框架）
@data_analysis.route("/get_radar_host", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取雷达主机")
@handle_database_exceptions
def web_get_radar_host() -> ApiResponse:
    """获取雷达主机 - 使用新验证框架的简化版本"""
    try:
        # 验证请求数据
        data = RequestValidator.get_json_or_fail()
        radar_id = (
            RequestValidator.validate_field(data, "radar_ID")
            .required()
            .not_empty()
            .unwrap()
        )

        # 使用数据库助手查询雷达
        helper = db_helper(db_base_data_radar)
        radar = helper.find_one_or_fail(
            {"ID": radar_id}, "雷达不存在", "RADAR_NOT_FOUND"
        )

        # 验证坐标字段
        coordinates = radar.get("coordinates", {})
        if isinstance(coordinates, dict) and len(coordinates) == 0:  # type: ignore
            raise ValidationError("雷达未设置坐标", "COORDINATES_EMPTY", 500)

        return (
            jsonify(
                {
                    "status": "success",
                    "message": "雷达坐标加载成功",
                    "data": coordinates,
                }
            ),
            200,
        )

    except ValidationError as e:
        return e.to_response()


# 获取最新图像 - 原版本（保留作为对比）
@data_analysis.route("/get_lastest_Img_old", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取最新图像")
@handle_database_exceptions
def web_lastest_Img_old() -> ApiResponse:
    data = request.get_json()  # 获取前端提交的 JSON 数据
    if (error := ApiValidator.validate_request_data(data)) is not None:
        return error
    assert data is not None

    # print(data)
    radar_ID = data.get("radar_ID", "")
    if (error := ApiValidator.validate_id(radar_ID, "radar_ID")) is not None:
        return error
    assert radar_ID is not None

    mission_ID = data.get("mission_ID", "")
    if (error := ApiValidator.validate_id(mission_ID, "mission_ID")) is not None:
        return error
    assert mission_ID is not None

    # 使用专有雷达类
    db_config = DatabaseConfig(db_name=radar_ID)
    db_this_radar = db_config.init_app(app)

    db_img_data = db_this_radar["img_data_" + mission_ID]

    base_data_doc = db_img_data.find_one(
        {"任务ID": int(mission_ID)}, sort=[("时间戳", -1)]
    )
    if base_data_doc is None:
        return jsonify({"status": "error", "message": "获取错误"}), 500

    file_path = base_data_doc.get("road_xy", "")
    if file_path is None:
        return jsonify({"status": "error", "message": "获取错误"}), 500

    print("http://127.0.0.1:5000/" + file_path)

    return (
        jsonify({"status": "success", "message": "http://127.0.0.1:5000/" + file_path}),
        200,
    )


# 获取最新图像 - 新版本（使用验证框架）
@data_analysis.route("/get_lastest_Img", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取最新图像")
@handle_database_exceptions
def web_lastest_Img() -> ApiResponse:
    """获取最新图像 - 使用新验证框架的简化版本"""
    try:
        # 验证请求数据
        data = RequestValidator.get_json_or_fail()

        # 链式验证多个字段
        radar_id = (
            RequestValidator.validate_field(data, "radar_ID")
            .required()
            .not_empty()
            .unwrap()
        )

        mission_id = (
            RequestValidator.validate_field(data, "mission_ID")
            .required()
            .not_empty()
            .custom(lambda x: x.isdigit(), "任务ID必须是数字", "INVALID_MISSION_ID")
            .unwrap()
        )

        # 数据库操作
        db_config = DatabaseConfig(db_name=radar_id)
        db_this_radar = db_config.init_app(app)

        # 数据库连接检查（实际上不会为None，但保留检查逻辑）
        # if db_this_radar is None:
        #     raise ValidationError("雷达数据库连接失败", "DB_CONNECTION_ERROR", 500)

        collection = db_this_radar[f"img_data_{mission_id}"]
        helper = db_helper(collection)

        # 查找最新图像记录
        base_data_doc = helper.find_one_or_fail(
            {"任务ID": int(mission_id)}, "未找到图像数据", "IMAGE_DATA_NOT_FOUND"
        )

        # 验证文件路径
        file_path = (
            validate(base_data_doc.get("road_xy"), "图像路径")
            .required("图像路径不存在")
            .not_empty("图像路径为空")
            .unwrap()
        )

        return (
            jsonify(
                {"status": "success", "message": f"http://127.0.0.1:5000/{file_path}"}
            ),
            200,
        )

    except ValidationError as e:
        return e.to_response()


# 获取历史图像 - 原版本（保留作为对比）
@data_analysis.route("/get_history_Img_old", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取历史图像")
@handle_database_exceptions
def web_history_Img_old():
    data: Optional[DataType] = request.get_json()  # 获取前端提交的 JSON 数据
    if (error := ApiValidator.validate_request_data(data)) is not None:
        return error
    assert data is not None

    radar_ID = data["radar_ID"]
    mission_ID = data["mission_ID"]
    begin_time = data["begin_time"]  # type: ignore
    end_time = data["end_time"]
    db_this_radar = client[radar_ID]  # type: ignore
    db_img_data = db_this_radar["img_data_" + mission_ID]  # type: ignore
    end_time = datetime.strptime(
        end_time, "%Y-%m-%d %H:%M"
    ).timestamp()  # 解析为 datetime 对象,后转为时间戳
    print(end_time)
    base_data_doc = db_img_data.find_one(  # type: ignore
        {"任务ID": int(mission_ID), "时间戳": {"$lte": end_time + 1}},
        sort=[("时间戳", -1)],
    )
    print(base_data_doc)  # type: ignore
    file_path = base_data_doc["road_xy"]  # type: ignore
    print("http://127.0.0.1:5000/" + file_path)  # type: ignore
    return jsonify(
        {
            "status": "success",
            "message": {"img_url": "http://127.0.0.1:5000/" + file_path},
        }
    )


# 获取历史图像 - 新版本（使用验证框架）
@data_analysis.route("/get_history_Img", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取历史图像")
@handle_database_exceptions
def web_history_Img():
    """获取历史图像 - 使用新验证框架的简化版本"""
    try:
        # 验证请求数据
        data = RequestValidator.get_json_or_fail()

        # 链式验证多个字段
        radar_id = (
            RequestValidator.validate_field(data, "radar_ID")
            .required()
            .not_empty()
            .unwrap()
        )

        mission_id = (
            RequestValidator.validate_field(data, "mission_ID")
            .required()
            .not_empty()
            .custom(lambda x: x.isdigit(), "任务ID必须是数字", "INVALID_MISSION_ID")
            .unwrap()
        )

        end_time_str = (
            RequestValidator.validate_field(data, "end_time")
            .required()
            .not_empty()
            .unwrap()
        )

        # 验证时间格式并转换
        try:
            end_time_timestamp = datetime.strptime(
                end_time_str, "%Y-%m-%d %H:%M"
            ).timestamp()
        except ValueError:
            raise ValidationError(
                "时间格式错误，应为 YYYY-MM-DD HH:MM", "INVALID_TIME_FORMAT"
            )

        # 数据库操作
        db_config = DatabaseConfig(db_name=radar_id)
        db_this_radar = db_config.init_app(app)
        collection = db_this_radar[f"img_data_{mission_id}"]
        # helper = db_helper(collection)  # 未使用，注释掉

        # 查找历史图像记录
        query = {"任务ID": int(mission_id), "时间戳": {"$lte": end_time_timestamp + 1}}  # type: ignore

        # 使用原生查询（因为需要排序）
        base_data_doc = collection.find_one(query, sort=[("时间戳", -1)])  # type: ignore
        if base_data_doc is None:
            raise ValidationError(
                "未找到指定时间的图像数据", "IMAGE_DATA_NOT_FOUND", 404
            )

        # 验证文件路径
        file_path = (
            validate(base_data_doc.get("road_xy"), "图像路径")
            .required("图像路径不存在")
            .not_empty("图像路径为空")
            .unwrap()
        )

        img_url = f"http://127.0.0.1:5000/{file_path}"

        return (
            jsonify(
                {
                    "status": "success",
                    "message": {"img_url": img_url},
                }
            ),
            200,
        )

    except ValidationError as e:
        return e.to_response()
