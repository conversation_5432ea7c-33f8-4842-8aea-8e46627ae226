# 验证框架迁移指南

## 🎯 **问题分析**

当前项目中存在大量重复的存在性检查代码，主要表现在：

1. **请求数据验证**：每个API都需要检查 `request.get_json()` 是否为空
2. **字段验证**：重复的 `data.get("field")` 和空值检查
3. **数据库查询验证**：重复的 `find_one()` 和 `None` 检查
4. **错误处理**：相似的错误响应格式

## 🚀 **解决方案**

新的验证框架提供了三种使用方式：

### 1. 装饰器模式（最简洁）

```python
@validate_request("scene_ID")  # 自动验证必需字段
@with_database_doc("scene", "scene_ID", "scene", "场景不存在")  # 自动查询文档
def get_scene_coordinates_v2(data, validated, scene):
    # 直接使用验证后的数据
    coordinates = (validate(scene.get("coordinates"), "场景坐标")
                  .required("场景坐标不存在")
                  .not_empty("雷达未设置场景坐标")
                  .raise_if_invalid())
    
    return jsonify({"status": "success", "data": coordinates}), 200
```

### 2. 数据库助手模式（中等复杂度）

```python
def get_radar_host_v2():
    try:
        # 验证请求数据
        data = RequestValidator.get_json_or_fail()
        radar_id = (RequestValidator.validate_field(data, "radar_ID")
                   .required()
                   .not_empty()
                   .raise_if_invalid())
        
        # 使用数据库助手
        helper = db_helper(db_base_data_radar)
        radar = helper.find_one_or_fail({"ID": radar_id}, "雷达不存在")
        
        return jsonify({"status": "success", "data": radar}), 200
        
    except ValidationError as e:
        return e.to_response()
```

### 3. 链式验证器模式（最灵活）

```python
def get_latest_image_v2():
    try:
        data = RequestValidator.get_json_or_fail()
        
        # 链式验证多个字段
        radar_id = (validate(data.get("radar_ID"), "雷达ID")
                   .required()
                   .not_empty()
                   .min_length(1)
                   .raise_if_invalid())
        
        mission_id = (validate(data.get("mission_ID"), "任务ID")
                     .required()
                     .custom(lambda x: x.isdigit(), "任务ID必须是数字")
                     .raise_if_invalid())
        
        return jsonify({"status": "success"}), 200
        
    except ValidationError as e:
        return e.to_response()
```

## 📊 **代码对比**

### 原版本（冗长）
```python
def get_scene_coordinates_old():
    # 验证请求数据 - 5行代码
    data = request.get_json()
    if error := ApiValidator.validate_request_data(data):
        return error
    assert data is not None

    # 验证scene_ID - 4行代码
    scene_id = data.get("scene_ID")
    if error := ApiValidator.validate_id(scene_id, "scene_ID"):
        return error
    assert scene_id is not None

    # 查询场景 - 3行代码
    db_scene_doc = db_base_data_scene.find_one({"_id": scene_id})
    if db_scene_doc is None:
        return jsonify({"error": "场景不存在"}), 404

    # 验证坐标字段 - 8行代码
    send_data = db_scene_doc.get("coordinates")
    if send_data is None:
        return jsonify({"error": "场景坐标不存在"}), 404
    if len(send_data) == 0:
        return jsonify({"error": "雷达未设置场景坐标"}), 404

    return jsonify({"status": "success", "data": send_data}), 200
    # 总计：20行代码
```

### 新版本（简洁）
```python
def get_scene_coordinates():
    try:
        # 验证请求数据和字段 - 4行代码
        data = RequestValidator.get_json_or_fail()
        scene_id = (RequestValidator.validate_field(data, "scene_ID")
                   .required().not_empty().raise_if_invalid())
        
        # 查询场景 - 3行代码
        helper = db_helper(db_base_data_scene)
        scene = helper.find_one_or_fail({"_id": scene_id}, "场景不存在")
        
        # 验证坐标字段 - 4行代码
        coordinates = (validate(scene.get("coordinates"), "场景坐标")
                      .required("场景坐标不存在")
                      .not_empty("雷达未设置场景坐标")
                      .raise_if_invalid())
        
        return jsonify({"status": "success", "data": coordinates}), 200
        
    except ValidationError as e:
        return e.to_response()
    # 总计：14行代码，减少30%
```

## 🔧 **迁移步骤**

### 步骤1：安装验证框架
```python
# 在需要的文件中导入
from validators import (
    validate_request, with_database_doc, validate, 
    db_helper, RequestValidator, ValidationError
)
```

### 步骤2：选择合适的模式
- **简单API**：使用装饰器模式
- **中等复杂度**：使用数据库助手模式  
- **复杂验证**：使用链式验证器模式

### 步骤3：逐步迁移
1. 保留原函数（重命名为 `_old`）
2. 创建新版本函数
3. 测试新版本
4. 删除旧版本

## 📈 **收益分析**

### 代码量减少
- **平均减少30-50%的验证代码**
- **消除重复的错误处理逻辑**
- **统一的错误响应格式**

### 可维护性提升
- **链式API更易读**
- **集中的验证逻辑**
- **类型安全的验证**

### 错误处理改进
- **统一的错误格式**
- **更好的错误信息**
- **自动的HTTP状态码**

## 🎨 **高级用法**

### 自定义验证器
```python
def validate_coordinates(coords):
    """自定义坐标验证"""
    if not isinstance(coords, dict):
        return False
    return all(key in coords for key in ["latitude", "longitude"])

# 使用自定义验证器
coordinates = (validate(data.get("coordinates"), "坐标")
              .required()
              .custom(validate_coordinates, "坐标格式不正确")
              .raise_if_invalid())
```

### 批量验证
```python
# 批量验证多个字段
validated_fields = RequestValidator.extract_fields(
    data, "scene_ID", "radar_IDs", "time_range"
)
```

### 条件验证
```python
# 根据条件进行不同的验证
if data.get("type") == "advanced":
    coordinates = (validate(data.get("coordinates"))
                  .required()
                  .custom(validate_advanced_coords, "高级坐标格式错误")
                  .raise_if_invalid())
```

## 🚨 **注意事项**

1. **异常处理**：所有使用新框架的函数都需要捕获 `ValidationError`
2. **类型安全**：框架提供了更好的类型提示
3. **向后兼容**：可以与现有的验证逻辑共存
4. **性能**：验证框架有轻微的性能开销，但可忽略不计

## 📝 **最佳实践**

1. **优先使用装饰器模式**：对于简单的CRUD操作
2. **复杂验证使用链式API**：对于需要多步验证的场景
3. **统一错误处理**：始终使用 `ValidationError` 和 `e.to_response()`
4. **渐进式迁移**：不要一次性重写所有代码
5. **保留测试**：确保新版本与旧版本行为一致
