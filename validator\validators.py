"""
高级验证和存在性检查框架
提供链式验证、自动错误处理和简化的API
"""

from flask import jsonify, request, Response
from typing import Any, Dict, List, Optional, Callable, TypeVar, Generic, Tuple
from pymongo.collection import Collection
from pymongo.errors import ConnectionFailure, OperationFailure
from bson.errors import InvalidDocument
from functools import wraps
import logging

# --- 设置 ---
logger = logging.getLogger(__name__)

# --- 类型定义 ---
T = TypeVar("T")
ApiResponse = Tuple[Response, int]
F = Callable[..., ApiResponse]


# --- 核心异常类 ---


class ValidationError(Exception):
    """
    自定义验证错误异常。
    此类异常代表可预期的、业务逻辑层面的错误，应被捕获并转换为对用户友好的API响应。
    """

    def __init__(
        self, message: str, code: str = "VALIDATION_ERROR", status_code: int = 400
    ):
        """
        初始化验证错误

        Args:
            message (str): 错误信息
            code (str, optional): 错误代码，默认为"VALIDATION_ERROR"
            status_code (int, optional): HTTP状态码，默认为400
        """
        self.message = message
        self.code = code
        self.status_code = status_code
        super().__init__(message)

    def to_response(self) -> Tuple[Response, int]:
        """
        将验证错误转换为Flask响应

        Returns:
            Tuple[Response, int]: 包含错误信息的JSON响应和HTTP状态码的元组
        """
        return jsonify({"error": self.message, "code": self.code}), self.status_code


# --- 验证器核心类 ---


class ValidationResult(Generic[T]):
    """验证结果包装器"""

    def __init__(
        self, value: Optional[T] = None, error: Optional[ValidationError] = None
    ):
        """
        初始化验证器实例

        Args:
            value (Optional[T]): 待验证的值，可为None
            error (Optional[ValidationError]): 验证错误对象，None表示验证通过

        Attributes:
            value: 存储传入的待验证值
            error: 存储验证错误信息
            is_valid: 布尔值，表示验证是否通过(error为None时为True)
        """
        self.value = value
        self.error = error
        self.is_valid = error is None

    def unwrap(self) -> T:
        """解包结果对象，返回内部值。如果存在错误则抛出异常。

        Returns:
            内部存储的值。

        Raises:
            Exception: 如果结果对象包含错误则抛出。
        """
        if self.error:
            raise self.error
        return self.value  # type: ignore

    def unwrap_or(self, default: T) -> T:
        """如果当前值为有效则返回该值，否则返回指定的默认值。

        Args:
            default (T): 当值无效时返回的默认值

        Returns:
            T: 有效值或默认值
        """
        return self.value if self.is_valid else default  # type: ignore


class ChainValidator:
    """链式验证器 - 支持流畅的验证链"""

    def __init__(self, value: Any = None, context: str = ""):
        """
        初始化验证器实例

        Args:
            value (Any): 待验证的值，默认为None
            context (str): 验证上下文信息，默认为空字符串
        Attributes:
            errors: 存储验证错误的列表
        """
        self.value = value
        self.context = context
        self.errors: List[ValidationError] = []
        self._stopped = False  # 如果已触发 required 错误，则停止后续检查

    def _check_and_continue(self) -> bool:
        """内部方法，检查是否应继续验证链。
        当且仅当值不为None，且通过 required 检查时，才继续执行后续验证。
        """
        return not self._stopped and self.value is not None

    def required(self, message: Optional[str] = None) -> "ChainValidator":
        """
        检查字段值是否存在。如果字段值为None，则添加验证错误。

        Args:
            message (Optional[str]): 自定义错误消息，默认为"{字段名}不能为空"

        Returns:
            ChainValidator: 返回当前验证器实例以支持链式调用

        Raises:
            无显式抛出异常，但会在self.errors中添加ValidationError
        """
        if self.value is None:
            error_msg = message or f"{self.context}不能为空"
            self.errors.append(ValidationError(error_msg, "REQUIRED_FIELD_MISSING"))
            self._stopped = True
        return self

    def not_empty(self, message: Optional[str] = None) -> "ChainValidator":
        """验证值是否为空，
        检查字符串、列表或字典类型的值是否为空（空字符串、空列表或空字典）

        Args:
            message (Optional[str]): 自定义错误消息，默认为"{context}不能为空"

        Returns:
            ChainValidator: 返回当前验证器实例以支持链式调用

        Raises:
            无显式抛出异常，但会将错误添加到self.errors列表中
        """
        if self._check_and_continue() and (
            (isinstance(self.value, str) and not self.value.strip())
            or (isinstance(self.value, (list, dict)) and len(self.value) == 0)  # type: ignore
        ):
            error_msg = message or f"{self.context}不能为空"
            self.errors.append(ValidationError(error_msg, "EMPTY_VALUE"))
        return self

    def min_length(
        self, length: int, message: Optional[str] = None
    ) -> "ChainValidator":
        """
        验证值的长度是否不小于指定最小长度。

        Args:
            length (int): 最小长度要求
            message (Optional[str]): 自定义错误信息，默认为None

        Returns:
            ChainValidator: 返回当前验证器实例以支持链式调用

        Raises:
            当值长度小于指定长度时，会添加MIN_LENGTH_ERROR验证错误
        """
        if (
            self._check_and_continue()
            and hasattr(self.value, "__len__")
            and len(self.value) < length
        ):
            error_msg = message or f"{self.context}长度不能少于{length}"
            self.errors.append(ValidationError(error_msg, "MIN_LENGTH_ERROR"))
        return self

    def max_length(
        self, length: int, message: Optional[str] = None
    ) -> "ChainValidator":
        """验证字段值的最大长度限制

        Args:
            length: 允许的最大长度
            message: 自定义错误信息，默认为"字段长度不能超过{length}"

        Returns:
            ChainValidator: 返回当前验证器实例以支持链式调用

        Raises:
            当字段值长度超过限制时，会添加MAX_LENGTH_ERROR到errors列表
        """
        if (
            self._check_and_continue()
            and hasattr(self.value, "__len__")
            and len(self.value) > length
        ):
            error_msg = message or f"{self.context}长度不能超过{length}"
            self.errors.append(ValidationError(error_msg, "MAX_LENGTH_ERROR"))
        return self

    def custom(
        self,
        validator_func: Callable[[Any], bool],
        message: str,
        code: str = "CUSTOM_VALIDATION_ERROR",
    ) -> "ChainValidator":
        """
        自定义验证方法

        Args:
            validator_func: 自定义验证函数，接收任意类型参数并返回布尔值
            message: 验证失败时的错误信息
            code: 验证失败时的错误代码，默认为"CUSTOM_VALIDATION_ERROR"

        Returns:
            ChainValidator: 返回当前验证器实例以支持链式调用

        Raises:
            如果当前值不为None且验证函数返回False，则会将错误信息添加到errors列表中
        """
        if self._check_and_continue() and not validator_func(self.value):
            self.errors.append(ValidationError(message, code))
        return self

    def result(self) -> ValidationResult[Any]:
        """
        获取验证结果

        Returns:
            ValidationResult: 如果存在错误则返回第一个错误信息，否则返回验证通过的值
        """
        if self.errors:
            return ValidationResult(error=self.errors[0])
        return ValidationResult(value=self.value)

    def unwrap(self) -> Any:
        """如果验证失败，立即抛出异常，否则返回值。"""
        return self.result().unwrap()

    def unwrap_or(self, default: T) -> T:
        """
        如果验证通过，则返回值；如果验证失败或值不存在，则返回指定的默认值。

        Args:
            default: 默认值

        Returns:
            Any: 验证通过的值或默认值
        """
        # The logic is now delegated to the result object itself.
        return self.result().unwrap_or(default)


class DatabaseHelper:
    """数据库查询助手 - 自动处理存在性检查"""

    def __init__(self, collection: Collection[Dict[str, Any]]):
        """初始化验证器实例

        Args:
            collection (Collection[Dict[str, Any]]): 待验证的数据集合，每个元素为字典类型
        """
        self.collection = collection

    def find_one_or_fail(
        self,
        query: Dict[str, Any],
        error_message: str = "记录不存在",
        error_code: str = "RECORD_NOT_FOUND",
    ) -> Dict[str, Any]:
        """
        根据查询条件查找单个文档，若不存在则抛出验证错误

        Args:
            query (Dict[str, Any]): 查询条件字典
            error_message (str, optional): 记录不存在时的错误信息，默认为"记录不存在"
            error_code (str, optional): 记录不存在时的错误代码，默认为"RECORD_NOT_FOUND"

        Returns:
            Dict[str, Any]: 查询到的文档

        Raises:
            ValidationError: 当查询结果为空时抛出404错误
        """
        doc = self.collection.find_one(query)
        if doc is None:
            raise ValidationError(error_message, error_code, 404)
        return doc

    def find_field_or_fail(
        self,
        query: Dict[str, Any],
        field: str,
        error_message: Optional[str] = None,
        error_code: str = "FIELD_NOT_FOUND",
    ) -> Any:
        """
        在查询结果中查找指定字段，若字段不存在则抛出验证错误

        Args:
            query: 查询条件字典
            field: 要查找的字段名
            error_message: 自定义错误信息，默认为"字段 {field} 不存在"
            error_code: 错误代码，默认为"FIELD_NOT_FOUND"

        Returns:
            查询到的字段值

        Raises:
            ValidationError: 当字段不存在时抛出，包含错误信息和404状态码
        """
        doc = self.find_one_or_fail(query)
        value = doc.get(field)
        if value is None:
            error_msg = error_message or f"字段 {field} 不存在"
            raise ValidationError(error_msg, error_code, 404)
        return value

    def find_non_empty_field(
        self,
        query: Dict[str, Any],
        field: str,
        error_message: Optional[str] = None,
        error_code: str = "FIELD_EMPTY",
    ) -> Any:
        """
        在查询字典中查找指定字段并验证其非空。

        Args:
            query: 要查询的字典
            field: 要查找的字段名
            error_message: 自定义错误信息，默认为None
            error_code: 错误代码，默认为"FIELD_EMPTY"

        Returns:
            找到的字段值

        Raises:
            ValidationError: 如果字段不存在或值为空(list/dict)
        """
        value = self.find_field_or_fail(query, field, error_message, error_code)
        if isinstance(value, (list, dict)) and len(value) == 0:  # type: ignore
            error_msg = error_message or f"字段 {field} 为空"
            raise ValidationError(error_msg, error_code, 404)
        return value  # type: ignore


class RequestValidator:
    """请求验证器 - 简化请求数据验证"""

    @staticmethod
    def get_json_or_fail() -> Dict[str, Any]:
        """
        静态方法 - 获取并验证请求中的JSON数据

        从请求中获取JSON格式数据，如果数据无效或不存在则抛出ValidationError异常

        它允许空的JSON对象

        Returns:
            Dict[str, Any]: 解析成功的JSON字典数据

        Raises:
            ValidationError: 当请求体不是有效JSON格式时抛出，附带错误信息"请求体必须是有效的JSON格式"和错误代码"INVALID_REQUEST_BODY"
        """
        data = request.get_json(silent=True)
        if data is None:
            raise ValidationError("请求体必须是有效的JSON格式", "INVALID_REQUEST_BODY")
        return data

    @staticmethod
    def extract_and_validate(data: Dict[str, Any], *fields: str) -> Dict[str, Any]:
        """
        从请求数据中提取指定字段并进行基本验证

        Args:
            data (Dict[str, Any]): 待验证的JSON数据

        Returns:
            Dict[str, Any]: 验证后的数据字典

        Raises:
            ValidationError: 当字段不存在时抛出，附带错误信息"{field}不能为空"和错误代码"REQUIRED_FIELD_MISSING"
        """
        validated_data: Dict[str, Any] = {}
        for field in fields:
            value = data.get(field)
            # 仅做存在性检查，更复杂的验证留给业务逻辑层
            validated_data[field] = validate(value, field).required().unwrap()
        return validated_data


# 便捷函数
def validate(value: Any, context: str = "") -> ChainValidator:
    """创建链式验证器的便捷函数"""
    return ChainValidator(value, context)


def db_helper(collection: Collection[Dict[str, Any]]) -> DatabaseHelper:
    """创建数据库助手的便捷函数"""
    return DatabaseHelper(collection)


# --- 装饰器 ---


def handle_api_exceptions(info: str = "") -> Callable[[F], F]:
    """最外层异常捕获器，处理未知服务器错误。"""

    def decorator(f: F) -> F:
        @wraps(f)
        def decorated_function(*args: Any, **kwargs: Any) -> ApiResponse:
            try:
                return f(*args, **kwargs)
            except ValidationError as e:
                # 捕获可预期的验证错误并返回格式化响应
                logger.warning(f"Validation failed: {e.message} (Code: {e.code})")
                return e.to_response()
            except Exception as e:
                # 捕获所有其他未知异常
                error_info = f"{info}失败" if info else "服务器内部错误"
                logger.error(f"{error_info}: {e}", exc_info=True)
                return jsonify({"error": error_info, "details": str(e)}), 500

        return decorated_function  # type: ignore[return-value]

    return decorator


def handle_database_exceptions(f: F) -> F:
    """数据库特定异常捕获器。"""

    @wraps(f)
    def decorated_function(*args: Any, **kwargs: Any) -> ApiResponse:
        try:
            return f(*args, **kwargs)
        except ConnectionFailure as e:
            logger.error(f"数据库连接失败: {e}")
            return jsonify({"error": "数据库服务不可用"}), 503
        except OperationFailure as e:
            logger.error(f"数据库操作失败: {e}")
            return jsonify({"error": "数据库操作失败"}), 500
        except InvalidDocument as e:
            logger.error(f"无效文档格式: {e}")
            return jsonify({"error": "提交的数据格式错误"}), 400

    return decorated_function  # type: ignore[return-value]


def validate_request(*required_fields: str) -> Callable[[F], F]:
    """
    请求验证装饰器，自动验证JSON请求体和指定的必需字段。
    如果验证成功，会将原始请求数据 `data` 和已验证的字段 `validated` 注入到被装饰函数的kwargs中。
    """

    def decorator(f: F) -> F:
        @wraps(f)
        def decorated_function(*args: Any, **kwargs: Any) -> ApiResponse:
            # 1. 确保请求体是JSON
            data = RequestValidator.get_json_or_fail()
            # 2. 验证所有必需字段都存在
            validated_data = RequestValidator.extract_and_validate(
                data, *required_fields
            )
            # 3. 注入到kwargs并调用原函数
            kwargs["data"] = data
            kwargs["validated"] = validated_data
            return f(*args, **kwargs)

        return decorated_function

    return decorator


# --- 新的、更强大的数据库装饰器 ---
def _get_query_value(source: str, kwargs: Dict[str, Any]) -> Any:
    """
    内部帮助函数，从请求中（URL参数或验证后的JSON）获取查询值。

    此函数按以下优先级顺序查找值:
    1.  Flask 路由的 URL 路径参数 (e.g., /users/<user_id>)。
    2.  由 @validate_request 装饰器提供的 `validated` 字典中的值 (来自请求体)。

    如果在这两个位置都找不到值，它会抛出一个详细的 ValidationError。

    Args:
        source (str): 要在 kwargs 或 validated 数据中查找的键名。
        kwargs (Dict[str, Any]): 视图函数接收到的关键字参数。

    Returns:
        Any: 找到的值。

    Raises:
        ValidationError: 如果在任何预期位置都找不到值。
    """
    # --- 1. 优先从 URL 路径参数中获取 ---
    # Flask 会将 <user_id> 这样的路径参数直接放入 kwargs
    value = kwargs.get(source)
    if value is not None:
        return value

    # --- 2. 其次，尝试从 @validate_request 提供的 validated 数据中获取 ---
    validated_data = kwargs.get("validated")
    if validated_data is not None and isinstance(validated_data, dict):
        value = validated_data.get(source)  # type: ignore
        if value is not None:
            return value  # type: ignore

    # --- 3. 如果都找不到，抛出详细的错误 ---
    # 构建一个有用的错误消息，告诉开发者我们搜索了哪里
    searched_locations = ["URL路径参数 (e.g., /items/<item_id>)"]
    if "validated" in kwargs:
        searched_locations.append("请求体验证后的JSON数据 (来自 @validate_request)")
    else:
        searched_locations.append(
            "请求体验证后的JSON数据 (注意: 未找到'validated'数据，您是否忘记使用@validate_request装饰器？)"
        )

    error_message = (
        f"数据库查询失败：无法找到必需的值 '{source}'。"
        f" 已在以下位置搜索: {'; '.join(searched_locations)}。"
        f" 请确保 '{source}' 存在于其中一个来源中。"
    )

    raise ValidationError(error_message, "MISSING_QUERY_VALUE", 400)


def with_doc(
    collection_name: str,
    query_by: Dict[str, str],
    inject_as: str = "doc",
    error_message: Optional[str] = None,
) -> Callable[[F], F]:
    """
    根据请求数据查找单个文档并将其注入到函数参数中。

    Args:
        collection_name (str): 要查询的MongoDB集合名称。
        query_by (Dict[str, str]): 一个字典，将请求字段名映射到数据库字段名。
            例如: {'_id': 'user_id'} 或 {'ID': 'radar_id'}
        inject_as (str): 将找到的文档注入kwargs时使用的键名。
        error_message (Optional[str]): 自定义未找到记录时的错误信息。
    """

    def decorator(f: F) -> F:
        @wraps(f)
        def decorated_function(*args: Any, **kwargs: Any) -> ApiResponse:
            # 从请求中获取查询值
            db_field, request_field = list(query_by.items())[0]

            # 获取查询值
            query_value = _get_query_value(request_field, kwargs)

            from app import mongo_db

            collection = mongo_db[collection_name]
            helper = db_helper(collection)

            query = {db_field: query_value}

            doc_not_found_msg = error_message or f"记录不存在"
            doc_not_found_code = f"{inject_as.upper()}_NOT_FOUND"

            document = helper.find_one_or_fail(
                query, doc_not_found_msg, doc_not_found_code
            )

            kwargs[inject_as] = document
            return f(*args, **kwargs)

        return decorated_function

    return decorator


def ensure_exists(
    collection_name: str,
    query_by: Dict[str, str],
    error_message: Optional[str] = None,
):
    """
    验证一个文档是否存在于数据库中，但不会将其注入。用于外键校验。

    比如被装饰的函数内部需要插入一个文档，但该文档必须存在，否则抛出异常。
    """

    def decorator(f: F) -> F:
        @wraps(f)
        def decorated_function(*args: Any, **kwargs: Any) -> ApiResponse:
            # 内部逻辑与 with_doc 类似，但使用更高效的查询
            db_field, request_field = list(query_by.items())[0]
            query_value = _get_query_value(request_field, kwargs)

            from app import mongo_db

            collection = mongo_db[collection_name]

            # 高效查询：只检查是否存在，不拉取数据
            # count_documents 在大多数情况下是最佳选择
            exists = collection.count_documents({db_field: query_value}, limit=1) > 0

            if not exists:
                err_msg = error_message or "关联的记录不存在"
                err_code = f"{collection_name.upper()}_REFERENCE_NOT_FOUND"
                raise ValidationError(
                    err_msg, err_code, 400
                )  # 通常外键不存在是 400 Bad Request

            # 应用内部装饰器来执行查找和错误处理
            return f(*args, **kwargs)

        return decorated_function

    return decorator


def with_nested_doc(
    parent_collection: str,
    parent_query_by: Dict[str, str],
    nested_list_field: str,
    nested_query_by: Dict[str, str],
    inject_as: str = "nested_doc",
    parent_inject_as: Optional[str] = None,
):
    """
    查找一个父文档，然后在它的一个数组字段中查找一个特定的嵌套文档。
    """

    def decorator(f: Callable[..., ApiResponse]) -> Callable[..., ApiResponse]:
        @wraps(f)
        def decorated_function(*args: Any, **kwargs: Any) -> ApiResponse:
            # 1. 查找父文档
            parent_doc_name = parent_inject_as or "_temp_parent"
            parent_decorator = with_doc(
                collection_name=parent_collection,
                query_by=parent_query_by,
                inject_as=parent_doc_name,
            )

            # 内部函数，用于在找到父文档后执行嵌套查找
            def find_nested_in_parent(*a: Any, **k: Any):
                parent_doc = k[parent_doc_name]

                nested_list = parent_doc.get(nested_list_field)
                if not isinstance(nested_list, list):
                    raise ValidationError(
                        f"父文档中字段 '{nested_list_field}' 不是一个列表",
                        "INVALID_NESTED_STRUCTURE",
                    )

                nested_db_field, nested_req_field = list(nested_query_by.items())[0]
                nested_query_value = _get_query_value(nested_req_field, k)

                found_item = None
                for item in nested_list:  # type: ignore
                    if str(item.get(nested_db_field)) == str(nested_query_value):  # type: ignore
                        found_item = item  # type: ignore
                        break

                if found_item is None:
                    raise ValidationError(
                        f"在列表中未找到指定记录", f"{inject_as.upper()}_NOT_FOUND", 404
                    )

                k[inject_as] = found_item
                if parent_inject_as is None:  # 如果用户不希望注入父文档，则清理掉
                    k.pop("_temp_parent", None)

                return f(*a, **k)

            # 启动链式查找
            return parent_decorator(find_nested_in_parent)(*args, **kwargs)

        return decorated_function

    return decorator
